# دليل التطوير - Balbalan ⚽

## 🛠️ إعداد بيئة التطوير

### 1. متطلبات النظام
- Flutter SDK 3.1.0+
- Dart 3.0.0+
- Android Studio / VS Code
- Git

### 2. إعداد المشروع
```bash
# استنساخ المشروع
git clone <repository-url>
cd flutter_application_1

# تحميل التبعيات
flutter pub get

# فحص النظام
flutter doctor
```

### 3. إعداد قاعدة البيانات (Supabase)

#### إنشاء المشروع
1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ مشروع جديد
3. احصل على URL و Anon Key

#### تحديث الإعدادات
```dart
// lib/constants/app_constants.dart
static const String supabaseUrl = 'YOUR_SUPABASE_URL';
static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
```

#### إنشاء الجداول
```sql
-- جدول الفرق
CREATE TABLE teams (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  short_name VARCHAR(50),
  logo TEXT,
  founded VARCHAR(10),
  venue VARCHAR(255),
  website VARCHAR(255),
  country VARCHAR(100),
  created_at TIMESTAMP DEFAULT NOW()
);

-- جدول المباريات
CREATE TABLE matches (
  id SERIAL PRIMARY KEY,
  home_team_id INTEGER REFERENCES teams(id),
  away_team_id INTEGER REFERENCES teams(id),
  match_date TIMESTAMP NOT NULL,
  status VARCHAR(50) DEFAULT 'SCHEDULED',
  minute INTEGER,
  home_score INTEGER DEFAULT 0,
  away_score INTEGER DEFAULT 0,
  league VARCHAR(100),
  venue VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW()
);

-- جدول الأهداف
CREATE TABLE goals (
  id SERIAL PRIMARY KEY,
  match_id INTEGER REFERENCES matches(id),
  player_name VARCHAR(255) NOT NULL,
  team_side VARCHAR(10) NOT NULL, -- 'home' or 'away'
  minute INTEGER NOT NULL,
  goal_type VARCHAR(50) DEFAULT 'REGULAR',
  created_at TIMESTAMP DEFAULT NOW()
);

-- جدول الهدافين
CREATE TABLE top_scorers (
  id SERIAL PRIMARY KEY,
  player_name VARCHAR(255) NOT NULL,
  team_id INTEGER REFERENCES teams(id),
  goals INTEGER DEFAULT 0,
  assists INTEGER DEFAULT 0,
  matches INTEGER DEFAULT 0,
  league VARCHAR(100),
  season VARCHAR(20),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 4. إعداد Firebase

#### إنشاء المشروع
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. أنشئ مشروع جديد
3. أضف تطبيق Android و iOS

#### تكوين Android
```bash
# تحميل google-services.json
# ضعه في android/app/
```

#### تكوين iOS
```bash
# تحميل GoogleService-Info.plist
# ضعه في ios/Runner/
```

## 🏗️ هيكل الكود

### نماذج البيانات
```dart
// lib/models/
├── team.dart          # نموذج الفريق
├── match.dart         # نموذج المباراة
├── player.dart        # نموذج اللاعب
└── statistics.dart    # نموذج الإحصائيات
```

### الخدمات
```dart
// lib/services/
├── api_service.dart       # خدمة API الخارجية
├── supabase_service.dart  # خدمة قاعدة البيانات
└── notification_service.dart # خدمة الإشعارات
```

### الصفحات
```dart
// lib/screens/
├── home_screen.dart           # الصفحة الرئيسية
├── match_detail_screen.dart   # تفاصيل المباراة
└── top_scorers_screen.dart    # ترتيب الهدافين
```

### المكونات
```dart
// lib/widgets/
├── match_card.dart        # بطاقة المباراة
├── league_selector.dart   # اختيار الدوري
├── statistics_widget.dart # عرض الإحصائيات
└── lineup_widget.dart     # عرض التشكيلة
```

## 🎨 دليل التصميم

### الألوان
```dart
// الألوان الأساسية
static const Color white = Color(0xFFFFFFFF);
static const Color darkNavy = Color(0xFF1A1D29);
static const Color darkPurple = Color(0xFF2D1B69);
static const Color red = Color(0xFFE53E3E);
static const Color grey = Color(0xFF718096);
```

### الخطوط
```dart
// الخطوط المستخدمة
- Poppins: للنصوص الإنجليزية
- Cairo: للنصوص العربية
```

### المسافات
```dart
// المسافات المعيارية
static const double paddingSmall = 8.0;
static const double paddingMedium = 16.0;
static const double paddingLarge = 24.0;
static const double paddingXLarge = 32.0;
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/
```

### إنشاء اختبارات جديدة
```dart
// test/widget_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:balbalan/main.dart';

void main() {
  testWidgets('App should load without errors', (WidgetTester tester) async {
    await tester.pumpWidget(const BalbalanApp());
    expect(find.text('Balbalan'), findsOneWidget);
  });
}
```

## 📱 البناء والنشر

### بناء للإنتاج
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

### النشر
```bash
# Google Play Store
# استخدم Android App Bundle (.aab)

# Apple App Store
# استخدم Xcode لرفع البناء
```

## 🔧 نصائح التطوير

### 1. إدارة الحالة
```dart
// استخدم Provider لإدارة الحالة
class MatchProvider extends ChangeNotifier {
  List<Match> _matches = [];
  
  List<Match> get matches => _matches;
  
  void updateMatches(List<Match> newMatches) {
    _matches = newMatches;
    notifyListeners();
  }
}
```

### 2. معالجة الأخطاء
```dart
try {
  final matches = await ApiService.getMatches();
  // معالجة البيانات
} catch (e) {
  // معالجة الخطأ
  print('Error: $e');
  // عرض رسالة للمستخدم
}
```

### 3. تحسين الأداء
```dart
// استخدم const للمكونات الثابتة
const Text('Static Text');

// استخدم ListView.builder للقوائم الطويلة
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
);
```

## 🐛 حل المشاكل الشائعة

### مشكلة التبعيات
```bash
flutter clean
flutter pub get
```

### مشكلة Gradle
```bash
cd android
./gradlew clean
cd ..
flutter build apk
```

### مشكلة iOS
```bash
cd ios
pod install
cd ..
flutter build ios
```

## 📚 مصادر مفيدة

- [Flutter Documentation](https://docs.flutter.dev/)
- [Supabase Documentation](https://supabase.com/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Material Design](https://material.io/design)

## 🤝 المساهمة

### قواعد الكود
1. استخدم أسماء واضحة للمتغيرات والدوال
2. أضف تعليقات للكود المعقد
3. اتبع نمط Dart الرسمي
4. اكتب اختبارات للمميزات الجديدة

### عملية المراجعة
1. أنشئ فرع جديد للميزة
2. اكتب الكود واختبره
3. أنشئ Pull Request
4. انتظر المراجعة والموافقة

---

**ملاحظة**: هذا الدليل يتم تحديثه باستمرار. تأكد من مراجعة أحدث إصدار قبل البدء في التطوير.
