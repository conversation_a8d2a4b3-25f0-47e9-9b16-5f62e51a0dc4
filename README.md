# flutter_application_1

A new Flutter project.

## Getting Started

# Balbalan ⚽

تطبيق جوال احترافي مخصص لعشاق كرة القدم، مطور باستخدام Flutter ويعمل على Android وiOS.

## 🌟 المميزات

### 📱 الصفحات الرئيسية
- **الصفحة الرئيسية**: عرض المباريات المباشرة والقادمة مع شريط اختيار الدوريات
- **تفاصيل المباراة**: تبويبات شاملة تشمل الأحداث، الإحصائيات، التشكيلة، التقييمات، والملخص
- **ترتيب الهدافين**: عرض أفضل الهدافين مع الصور والإحصائيات

### 🎨 التصميم
- **ألوان عصرية**: أبيض، كحلي غامق، بنفسجي داكن، أحمر، ورمادي
- **خطوط احترافية**: Poppins و Cairo مع دعم Bold للعناوين
- **واجهات مستجيبة**: تصميم متجاوب يعمل على جميع أحجام الشاشات
- **حركات سلسة**: انتقالات وحركات بصرية جذابة

### ⚡ الوظائف المتقدمة
- **تحديث مباشر**: دعم WebSocket للتحديثات الفورية
- **دعم اللغات**: العربية والإنجليزية مع RTL
- **إشعارات ذكية**: تنبيهات عند تسجيل الأهداف
- **قاعدة بيانات**: دعم Supabase و Firebase
- **إحصائيات مرئية**: رسوم بيانية للتسديدات والاستحواذ

## 🏗️ البنية التقنية

### 📁 هيكل المشروع
```
lib/
├── constants/          # الثوابت والألوان
├── models/            # نماذج البيانات
├── screens/           # الصفحات الرئيسية
├── widgets/           # المكونات المخصصة
├── services/          # خدمات API وقاعدة البيانات
├── utils/             # الأدوات المساعدة
└── providers/         # إدارة الحالة
```

### 🔧 التقنيات المستخدمة
- **Flutter**: إطار العمل الرئيسي
- **Provider**: إدارة الحالة
- **Supabase**: قاعدة البيانات الرئيسية
- **Firebase**: الإشعارات والتحليلات
- **Cached Network Image**: تحسين الصور
- **FL Chart**: الرسوم البيانية
- **Go Router**: التنقل المتقدم

## 🚀 التشغيل والتطوير

### متطلبات النظام
- Flutter SDK 3.1.0 أو أحدث
- Dart 3.0.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو iOS للاختبار

### خطوات التشغيل
1. **استنساخ المشروع**
   ```bash
   git clone <repository-url>
   cd flutter_application_1
   ```

2. **تحميل التبعيات**
   ```bash
   flutter pub get
   ```

3. **إعداد قاعدة البيانات**
   - إنشاء مشروع Supabase جديد
   - تحديث `lib/constants/app_constants.dart` بمعلومات المشروع
   - إنشاء الجداول المطلوبة

4. **إعداد Firebase**
   - إنشاء مشروع Firebase
   - إضافة ملفات التكوين للمنصات
   - تفعيل خدمة الإشعارات

5. **تشغيل التطبيق**
   ```bash
   flutter run
   ```

## 📊 نماذج البيانات

### مثال JSON للمباريات
```json
{
  "matches": [
    {
      "id": 1,
      "league": "Premier League",
      "home_team": "Man Utd",
      "away_team": "Forest",
      "score": "3-2",
      "status": "LIVE",
      "minute": 67,
      "goals": [
        {"minute": 17, "player": "Eriksen", "team": "home"},
        {"minute": 52, "player": "Casemiro", "team": "home"},
        {"minute": 76, "player": "Fernandes", "team": "home"}
      ],
      "statistics": {
        "shots": {"home": 18, "away": 9},
        "on_goal": {"home": 9, "away": 4},
        "possession": {"home": 67, "away": 33}
      }
    }
  ]
}
```

### مثال JSON للهدافين
```json
{
  "scorers": [
    {
      "player": "Harry Maguire",
      "club": "Man Utd",
      "goals": 18,
      "matches": 25,
      "photo": "player_photo_url"
    }
  ]
}
```

## 🎯 المميزات القادمة

- [ ] دعم المزيد من الدوريات
- [ ] إضافة الترتيب العام للفرق
- [ ] نظام المفضلة للفرق
- [ ] وضع الليل
- [ ] مشاركة النتائج على وسائل التواصل
- [ ] إحصائيات اللاعبين التفصيلية
- [ ] التنبؤ بنتائج المباريات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: فريق Balbalan
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: [www.balbalan.app](https://www.balbalan.app)

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير. بعض المميزات قد تتطلب إعداد إضافي أو مفاتيح API خارجية.
