import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color darkNavy = Color(0xFF1A1D29);
  static const Color darkPurple = Color(0xFF2D1B69);
  static const Color red = Color(0xFFE53E3E);
  static const Color grey = Color(0xFF718096);
  
  // Secondary Colors
  static const Color lightGrey = Color(0xFFF7FAFC);
  static const Color mediumGrey = Color(0xFFE2E8F0);
  static const Color darkGrey = Color(0xFF4A5568);
  
  // Live Match Colors
  static const Color liveGreen = Color(0xFF38A169);
  static const Color liveBackground = Color(0xFF1A202C);
  
  // Success & Error
  static const Color success = Color(0xFF48BB78);
  static const Color error = Color(0xFFE53E3E);
  static const Color warning = Color(0xFFED8936);
  
  // Text Colors
  static const Color primaryText = Color(0xFF2D3748);
  static const Color secondaryText = Color(0xFF718096);
  static const Color lightText = Color(0xFFA0AEC0);
  
  // Background Colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFF7FAFC);
  static const Color statisticsBackground = Color(0xFF1A1D29);
  
  // Gradient Colors
  static const LinearGradient liveGradient = LinearGradient(
    colors: [darkPurple, Color(0xFF553C9A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [white, lightGrey],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
