class AppConstants {
  // App Info
  static const String appName = 'Balbalan';
  static const String appVersion = '1.0.0';
  
  // API Constants
  static const String baseUrl = 'https://api.football-data.org/v4';
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
  
  // Leagues
  static const Map<String, String> leagues = {
    'premier_league': 'Premier League',
    'la_liga': 'La Liga',
    'bundesliga': 'Bundesliga',
    'serie_a': 'Serie A',
    'ligue_1': 'Ligue 1',
    'champions_league': 'Champions League',
  };
  
  static const Map<String, String> leagueLogos = {
    'premier_league': 'assets/logos/premier_league.png',
    'la_liga': 'assets/logos/la_liga.png',
    'bundesliga': 'assets/logos/bundesliga.png',
    'serie_a': 'assets/logos/serie_a.png',
    'ligue_1': 'assets/logos/ligue_1.png',
    'champions_league': 'assets/logos/champions_league.png',
  };
  
  // Match Status
  static const String statusScheduled = 'SCHEDULED';
  static const String statusLive = 'LIVE';
  static const String statusFinished = 'FINISHED';
  static const String statusPostponed = 'POSTPONED';
  static const String statusCancelled = 'CANCELLED';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Padding & Margins
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border Radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  
  // Icon Sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  
  // Team Logo Sizes
  static const double teamLogoSmall = 24.0;
  static const double teamLogoMedium = 32.0;
  static const double teamLogoLarge = 48.0;
  static const double teamLogoXLarge = 64.0;
  
  // Player Image Sizes
  static const double playerImageSmall = 40.0;
  static const double playerImageMedium = 60.0;
  static const double playerImageLarge = 80.0;
  
  // Cache Duration
  static const Duration cacheShort = Duration(minutes: 5);
  static const Duration cacheMedium = Duration(minutes: 15);
  static const Duration cacheLong = Duration(hours: 1);
  
  // WebSocket Events
  static const String wsMatchUpdate = 'match_update';
  static const String wsGoalScored = 'goal_scored';
  static const String wsMatchStarted = 'match_started';
  static const String wsMatchEnded = 'match_ended';
  
  // Notification Types
  static const String notificationGoal = 'goal';
  static const String notificationMatchStart = 'match_start';
  static const String notificationMatchEnd = 'match_end';
  static const String notificationRedCard = 'red_card';
  
  // Shared Preferences Keys
  static const String keyLanguage = 'language';
  static const String keyTheme = 'theme';
  static const String keyFavoriteTeams = 'favorite_teams';
  static const String keyNotifications = 'notifications_enabled';
  static const String keyFirstLaunch = 'first_launch';
}
