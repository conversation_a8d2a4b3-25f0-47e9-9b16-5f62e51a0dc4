import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Font Families
  static const String poppins = 'Poppins';
  static const String cairo = 'Cairo';
  
  // Headings
  static const TextStyle h1 = TextStyle(
    fontFamily: poppins,
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );
  
  static const TextStyle h2 = TextStyle(
    fontFamily: poppins,
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );
  
  static const TextStyle h3 = TextStyle(
    fontFamily: poppins,
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.primaryText,
  );
  
  static const TextStyle h4 = TextStyle(
    fontFamily: poppins,
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppColors.primaryText,
  );
  
  // Body Text
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: poppins,
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.primaryText,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: poppins,
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.primaryText,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontFamily: poppins,
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.secondaryText,
  );
  
  // Special Text Styles
  static const TextStyle scoreText = TextStyle(
    fontFamily: poppins,
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );
  
  static const TextStyle liveText = TextStyle(
    fontFamily: poppins,
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.white,
  );
  
  static const TextStyle minuteText = TextStyle(
    fontFamily: poppins,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: AppColors.red,
  );
  
  static const TextStyle teamName = TextStyle(
    fontFamily: poppins,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.primaryText,
  );
  
  static const TextStyle playerName = TextStyle(
    fontFamily: poppins,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.primaryText,
  );
  
  static const TextStyle statisticValue = TextStyle(
    fontFamily: poppins,
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.white,
  );
  
  static const TextStyle statisticLabel = TextStyle(
    fontFamily: poppins,
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.lightText,
  );
  
  // Arabic Text Styles
  static const TextStyle arabicH1 = TextStyle(
    fontFamily: cairo,
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );
  
  static const TextStyle arabicH2 = TextStyle(
    fontFamily: cairo,
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primaryText,
  );
  
  static const TextStyle arabicBody = TextStyle(
    fontFamily: cairo,
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.primaryText,
  );
  
  static const TextStyle arabicBodySmall = TextStyle(
    fontFamily: cairo,
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.secondaryText,
  );
}
