import 'team.dart';
import 'player.dart';
import 'statistics.dart';

class Match {
  final int id;
  final Team homeTeam;
  final Team awayTeam;
  final DateTime matchDate;
  final String status;
  final int? minute;
  final Score? score;
  final String? league;
  final String? venue;
  final List<Goal> goals;
  final MatchStatistics? statistics;
  final List<LineupPlayer>? homeLineup;
  final List<LineupPlayer>? awayLineup;

  Match({
    required this.id,
    required this.homeTeam,
    required this.awayTeam,
    required this.matchDate,
    required this.status,
    this.minute,
    this.score,
    this.league,
    this.venue,
    this.goals = const [],
    this.statistics,
    this.homeLineup,
    this.awayLineup,
  });

  factory Match.fromJson(Map<String, dynamic> json) {
    return Match(
      id: json['id'] ?? 0,
      homeTeam: Team.fromJson(json['homeTeam'] ?? {}),
      awayTeam: Team.fromJson(json['awayTeam'] ?? {}),
      matchDate: DateTime.parse(json['utcDate'] ?? json['matchDate'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? 'SCHEDULED',
      minute: json['minute'],
      score: json['score'] != null ? Score.fromJson(json['score']) : null,
      league: json['competition']?['name'] ?? json['league'],
      venue: json['venue'],
      goals: (json['goals'] as List<dynamic>?)
          ?.map((goal) => Goal.fromJson(goal))
          .toList() ?? [],
      statistics: json['statistics'] != null 
          ? MatchStatistics.fromJson(json['statistics']) 
          : null,
      homeLineup: (json['homeLineup'] as List<dynamic>?)
          ?.map((player) => LineupPlayer.fromJson(player))
          .toList(),
      awayLineup: (json['awayLineup'] as List<dynamic>?)
          ?.map((player) => LineupPlayer.fromJson(player))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'homeTeam': homeTeam.toJson(),
      'awayTeam': awayTeam.toJson(),
      'matchDate': matchDate.toIso8601String(),
      'status': status,
      'minute': minute,
      'score': score?.toJson(),
      'league': league,
      'venue': venue,
      'goals': goals.map((goal) => goal.toJson()).toList(),
      'statistics': statistics?.toJson(),
      'homeLineup': homeLineup?.map((player) => player.toJson()).toList(),
      'awayLineup': awayLineup?.map((player) => player.toJson()).toList(),
    };
  }

  bool get isLive => status == 'LIVE' || status == 'IN_PLAY';
  bool get isFinished => status == 'FINISHED' || status == 'FULL_TIME';
  bool get isScheduled => status == 'SCHEDULED' || status == 'TIMED';

  String get displayTime {
    if (isLive && minute != null) {
      return "$minute'";
    } else if (isFinished) {
      return "FT";
    } else if (isScheduled) {
      return "${matchDate.hour.toString().padLeft(2, '0')}:${matchDate.minute.toString().padLeft(2, '0')}";
    }
    return status;
  }

  @override
  String toString() {
    return 'Match{id: $id, homeTeam: ${homeTeam.name}, awayTeam: ${awayTeam.name}, status: $status}';
  }
}

class Score {
  final int? homeScore;
  final int? awayScore;
  final int? homeHalfTime;
  final int? awayHalfTime;

  Score({
    this.homeScore,
    this.awayScore,
    this.homeHalfTime,
    this.awayHalfTime,
  });

  factory Score.fromJson(Map<String, dynamic> json) {
    final fullTime = json['fullTime'] ?? json;
    final halfTime = json['halfTime'];
    
    return Score(
      homeScore: fullTime['home'],
      awayScore: fullTime['away'],
      homeHalfTime: halfTime?['home'],
      awayHalfTime: halfTime?['away'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fullTime': {
        'home': homeScore,
        'away': awayScore,
      },
      'halfTime': {
        'home': homeHalfTime,
        'away': awayHalfTime,
      },
    };
  }

  String get displayScore {
    if (homeScore != null && awayScore != null) {
      return '$homeScore - $awayScore';
    }
    return '- : -';
  }

  @override
  String toString() {
    return displayScore;
  }
}

class Goal {
  final int minute;
  final Player scorer;
  final Player? assist;
  final String team; // 'home' or 'away'
  final String? type; // 'REGULAR', 'PENALTY', 'OWN_GOAL'

  Goal({
    required this.minute,
    required this.scorer,
    this.assist,
    required this.team,
    this.type,
  });

  factory Goal.fromJson(Map<String, dynamic> json) {
    return Goal(
      minute: json['minute'] ?? 0,
      scorer: Player.fromJson(json['scorer'] ?? {}),
      assist: json['assist'] != null ? Player.fromJson(json['assist']) : null,
      team: json['team'] ?? '',
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'minute': minute,
      'scorer': scorer.toJson(),
      'assist': assist?.toJson(),
      'team': team,
      'type': type,
    };
  }

  @override
  String toString() {
    return 'Goal{minute: $minute, scorer: ${scorer.name}, team: $team}';
  }
}
