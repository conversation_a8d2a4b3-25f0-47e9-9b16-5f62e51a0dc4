import 'team.dart';

class Player {
  final int id;
  final String name;
  final String? position;
  final String? nationality;
  final int? age;
  final String? photo;
  final int? shirtNumber;
  final Team? team;

  Player({
    required this.id,
    required this.name,
    this.position,
    this.nationality,
    this.age,
    this.photo,
    this.shirtNumber,
    this.team,
  });

  factory Player.fromJson(Map<String, dynamic> json) {
    return Player(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      position: json['position'],
      nationality: json['nationality'],
      age: json['age'],
      photo: json['photo'],
      shirtNumber: json['shirtNumber'],
      team: json['team'] != null ? Team.fromJson(json['team']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'position': position,
      'nationality': nationality,
      'age': age,
      'photo': photo,
      'shirtNumber': shirtNumber,
      'team': team?.to<PERSON><PERSON>(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Player && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Player{id: $id, name: $name, position: $position}';
  }

  Player copyWith({
    int? id,
    String? name,
    String? position,
    String? nationality,
    int? age,
    String? photo,
    int? shirtNumber,
    Team? team,
  }) {
    return Player(
      id: id ?? this.id,
      name: name ?? this.name,
      position: position ?? this.position,
      nationality: nationality ?? this.nationality,
      age: age ?? this.age,
      photo: photo ?? this.photo,
      shirtNumber: shirtNumber ?? this.shirtNumber,
      team: team ?? this.team,
    );
  }
}

class TopScorer {
  final Player player;
  final Team team;
  final int goals;
  final int assists;
  final int matches;

  TopScorer({
    required this.player,
    required this.team,
    required this.goals,
    this.assists = 0,
    this.matches = 0,
  });

  factory TopScorer.fromJson(Map<String, dynamic> json) {
    return TopScorer(
      player: Player.fromJson(json['player'] ?? {}),
      team: Team.fromJson(json['team'] ?? {}),
      goals: json['goals'] ?? 0,
      assists: json['assists'] ?? 0,
      matches: json['matches'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'player': player.toJson(),
      'team': team.toJson(),
      'goals': goals,
      'assists': assists,
      'matches': matches,
    };
  }

  double get goalsPerMatch => matches > 0 ? goals / matches : 0.0;

  @override
  String toString() {
    return 'TopScorer{player: ${player.name}, team: ${team.name}, goals: $goals}';
  }
}
