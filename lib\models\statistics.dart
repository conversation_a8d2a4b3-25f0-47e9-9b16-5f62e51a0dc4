class MatchStatistics {
  final TeamStatistics homeTeam;
  final TeamStatistics awayTeam;

  MatchStatistics({
    required this.homeTeam,
    required this.awayTeam,
  });

  factory MatchStatistics.fromJson(Map<String, dynamic> json) {
    return MatchStatistics(
      homeTeam: TeamStatistics.fromJson(json['homeTeam'] ?? json['home'] ?? {}),
      awayTeam: TeamStatistics.fromJson(json['awayTeam'] ?? json['away'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'homeTeam': homeTeam.toJson(),
      'awayTeam': awayTeam.toJson(),
    };
  }
}

class TeamStatistics {
  final int shots;
  final int shotsOnTarget;
  final int possession;
  final int corners;
  final int fouls;
  final int yellowCards;
  final int redCards;
  final int offsides;
  final int passes;
  final int passAccuracy;

  TeamStatistics({
    this.shots = 0,
    this.shotsOnTarget = 0,
    this.possession = 0,
    this.corners = 0,
    this.fouls = 0,
    this.yellowCards = 0,
    this.redCards = 0,
    this.offsides = 0,
    this.passes = 0,
    this.passAccuracy = 0,
  });

  factory TeamStatistics.fromJson(Map<String, dynamic> json) {
    return TeamStatistics(
      shots: _parseIntValue(json['shots']),
      shotsOnTarget: _parseIntValue(json['shotsOnTarget'] ?? json['on_goal']),
      possession: _parseIntValue(json['possession']),
      corners: _parseIntValue(json['corners']),
      fouls: _parseIntValue(json['fouls']),
      yellowCards: _parseIntValue(json['yellowCards']),
      redCards: _parseIntValue(json['redCards']),
      offsides: _parseIntValue(json['offsides']),
      passes: _parseIntValue(json['passes']),
      passAccuracy: _parseIntValue(json['passAccuracy']),
    );
  }

  static int _parseIntValue(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      final parsed = int.tryParse(value.replaceAll('%', ''));
      return parsed ?? 0;
    }
    return 0;
  }

  Map<String, dynamic> toJson() {
    return {
      'shots': shots,
      'shotsOnTarget': shotsOnTarget,
      'possession': possession,
      'corners': corners,
      'fouls': fouls,
      'yellowCards': yellowCards,
      'redCards': redCards,
      'offsides': offsides,
      'passes': passes,
      'passAccuracy': passAccuracy,
    };
  }

  @override
  String toString() {
    return 'TeamStatistics{shots: $shots, possession: $possession%}';
  }
}

class LineupPlayer {
  final int id;
  final String name;
  final int shirtNumber;
  final String position;
  final bool isSubstitute;

  LineupPlayer({
    required this.id,
    required this.name,
    required this.shirtNumber,
    required this.position,
    this.isSubstitute = false,
  });

  factory LineupPlayer.fromJson(Map<String, dynamic> json) {
    return LineupPlayer(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      shirtNumber: json['shirtNumber'] ?? 0,
      position: json['position'] ?? '',
      isSubstitute: json['isSubstitute'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'shirtNumber': shirtNumber,
      'position': position,
      'isSubstitute': isSubstitute,
    };
  }

  @override
  String toString() {
    return 'LineupPlayer{name: $name, position: $position, number: $shirtNumber}';
  }
}

class PlayerRating {
  final int playerId;
  final String playerName;
  final double rating;
  final int minutes;
  final Map<String, dynamic> stats;

  PlayerRating({
    required this.playerId,
    required this.playerName,
    required this.rating,
    this.minutes = 0,
    this.stats = const {},
  });

  factory PlayerRating.fromJson(Map<String, dynamic> json) {
    return PlayerRating(
      playerId: json['playerId'] ?? 0,
      playerName: json['playerName'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      minutes: json['minutes'] ?? 0,
      stats: json['stats'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'playerId': playerId,
      'playerName': playerName,
      'rating': rating,
      'minutes': minutes,
      'stats': stats,
    };
  }

  String get ratingDisplay => rating.toStringAsFixed(1);

  @override
  String toString() {
    return 'PlayerRating{playerName: $playerName, rating: $rating}';
  }
}
