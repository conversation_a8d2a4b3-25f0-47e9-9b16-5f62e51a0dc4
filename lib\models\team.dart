class Team {
  final int id;
  final String name;
  final String shortName;
  final String logo;
  final String? founded;
  final String? venue;
  final String? website;
  final String? country;

  Team({
    required this.id,
    required this.name,
    required this.shortName,
    required this.logo,
    this.founded,
    this.venue,
    this.website,
    this.country,
  });

  factory Team.fromJson(Map<String, dynamic> json) {
    return Team(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      shortName: json['shortName'] ?? json['name'] ?? '',
      logo: json['logo'] ?? json['crest'] ?? '',
      founded: json['founded']?.toString(),
      venue: json['venue'],
      website: json['website'],
      country: json['country'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'shortName': shortName,
      'logo': logo,
      'founded': founded,
      'venue': venue,
      'website': website,
      'country': country,
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Team && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Team{id: $id, name: $name, shortName: $shortName}';
  }

  Team copyWith({
    int? id,
    String? name,
    String? shortName,
    String? logo,
    String? founded,
    String? venue,
    String? website,
    String? country,
  }) {
    return Team(
      id: id ?? this.id,
      name: name ?? this.name,
      shortName: shortName ?? this.shortName,
      logo: logo ?? this.logo,
      founded: founded ?? this.founded,
      venue: venue ?? this.venue,
      website: website ?? this.website,
      country: country ?? this.country,
    );
  }
}
