import 'package:flutter/material.dart';
import '../models/match.dart';
import '../services/api_service.dart';
import '../widgets/match_card.dart';
import '../widgets/league_selector.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';
import 'match_detail_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedLeague = 'premier_league';
  List<Match> _liveMatches = [];
  List<Match> _upcomingMatches = [];
  List<Match> _todayMatches = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadMatches();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMatches() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final futures = await Future.wait([
        ApiService.getLiveMatches(),
        ApiService.getUpcomingMatches(),
        ApiService.getTodayMatches(),
      ]);

      setState(() {
        _liveMatches = futures[0];
        _upcomingMatches = futures[1];
        _todayMatches = futures[2];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading matches: $e')),
        );
      }
    }
  }

  void _onLeagueSelected(String league) {
    setState(() {
      _selectedLeague = league;
    });
    _loadMatches();
  }

  void _navigateToMatchDetail(Match match) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MatchDetailScreen(match: match),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              floating: false,
              pinned: true,
              backgroundColor: AppColors.white,
              foregroundColor: AppColors.primaryText,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'Balbalan',
                  style: AppTextStyles.h2.copyWith(
                    color: AppColors.primaryText,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppColors.darkPurple,
                        AppColors.white,
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const SizedBox(height: 60),
                      LeagueSelector(
                        selectedLeague: _selectedLeague,
                        onLeagueSelected: _onLeagueSelected,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Tab Bar
            Container(
              color: AppColors.white,
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Live'),
                  Tab(text: 'Today'),
                  Tab(text: 'Upcoming'),
                ],
              ),
            ),
            
            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLiveTab(),
                  _buildTodayTab(),
                  _buildUpcomingTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_liveMatches.isEmpty) {
      return _buildEmptyState('No live matches at the moment');
    }

    return RefreshIndicator(
      onRefresh: _loadMatches,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
        itemCount: _liveMatches.length,
        itemBuilder: (context, index) {
          final match = _liveMatches[index];
          return MatchCard(
            match: match,
            isLive: true,
            onTap: () => _navigateToMatchDetail(match),
          );
        },
      ),
    );
  }

  Widget _buildTodayTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_todayMatches.isEmpty) {
      return _buildEmptyState('No matches today');
    }

    return RefreshIndicator(
      onRefresh: _loadMatches,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
        itemCount: _todayMatches.length,
        itemBuilder: (context, index) {
          final match = _todayMatches[index];
          return MatchCard(
            match: match,
            isLive: match.isLive,
            onTap: () => _navigateToMatchDetail(match),
          );
        },
      ),
    );
  }

  Widget _buildUpcomingTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_upcomingMatches.isEmpty) {
      return _buildEmptyState('No upcoming matches');
    }

    return RefreshIndicator(
      onRefresh: _loadMatches,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
        itemCount: _upcomingMatches.length,
        itemBuilder: (context, index) {
          final match = _upcomingMatches[index];
          return MatchCard(
            match: match,
            onTap: () => _navigateToMatchDetail(match),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.sports_soccer,
            size: 64,
            color: AppColors.grey,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            message,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ElevatedButton(
            onPressed: _loadMatches,
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }
}
