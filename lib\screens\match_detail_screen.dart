import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/match.dart';

import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';
import '../widgets/statistics_widget.dart';
import '../widgets/lineup_widget.dart';

class MatchDetailScreen extends StatefulWidget {
  final Match match;

  const MatchDetailScreen({
    Key? key,
    required this.match,
  }) : super(key: key);

  @override
  State<MatchDetailScreen> createState() => _MatchDetailScreenState();
}

class _MatchDetailScreenState extends State<MatchDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: AppColors.darkNavy,
              foregroundColor: AppColors.white,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppColors.liveGradient,
                  ),
                  child: _buildMatchHeader(),
                ),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Tab Bar
            Container(
              color: AppColors.white,
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                tabs: const [
                  Tab(text: 'Game'),
                  Tab(text: 'Statistics'),
                  Tab(text: 'Line Up'),
                  Tab(text: 'Ratings'),
                  Tab(text: 'Summary'),
                ],
              ),
            ),
            
            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildGameTab(),
                  _buildStatisticsTab(),
                  _buildLineUpTab(),
                  _buildRatingsTab(),
                  _buildSummaryTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMatchHeader() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // League and Status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.match.league ?? 'Unknown League',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.white,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: widget.match.isLive ? AppColors.red : AppColors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                  ),
                  child: Text(
                    widget.match.displayTime,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Teams and Score
            Row(
              children: [
                // Home Team
                Expanded(
                  child: _buildTeamSection(widget.match.homeTeam, true),
                ),
                
                // Score
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    children: [
                      if (widget.match.score != null)
                        Text(
                          widget.match.score!.displayScore,
                          style: AppTextStyles.h1.copyWith(
                            color: AppColors.white,
                            fontSize: 36,
                          ),
                        )
                      else
                        Text(
                          'vs',
                          style: AppTextStyles.h2.copyWith(
                            color: AppColors.white,
                          ),
                        ),
                      if (widget.match.isLive && widget.match.minute != null)
                        Text(
                          "${widget.match.minute}'",
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.white,
                          ),
                        ),
                    ],
                  ),
                ),
                
                // Away Team
                Expanded(
                  child: _buildTeamSection(widget.match.awayTeam, false),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamSection(team, bool isHome) {
    return Column(
      children: [
        // Team Logo
        Container(
          width: AppConstants.teamLogoLarge,
          height: AppConstants.teamLogoLarge,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.white,
            boxShadow: [
              BoxShadow(
                color: AppColors.darkNavy.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipOval(
            child: CachedNetworkImage(
              imageUrl: team.logo,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppColors.lightGrey,
                child: const Icon(
                  Icons.sports_soccer,
                  color: AppColors.grey,
                  size: 32,
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: AppColors.lightGrey,
                child: const Icon(
                  Icons.sports_soccer,
                  color: AppColors.grey,
                  size: 32,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        
        // Team Name
        Text(
          team.name,
          style: AppTextStyles.h4.copyWith(
            color: AppColors.white,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildGameTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Match Info
          _buildInfoCard(),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Goals Timeline
          if (widget.match.goals.isNotEmpty) ...[
            _buildGoalsTimeline(),
            const SizedBox(height: AppConstants.paddingMedium),
          ],
          
          // Venue Info
          if (widget.match.venue != null) ...[
            _buildVenueInfo(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatisticsTab() {
    if (widget.match.statistics == null) {
      return const Center(
        child: Text('No statistics available'),
      );
    }
    
    return StatisticsWidget(statistics: widget.match.statistics!);
  }

  Widget _buildLineUpTab() {
    if (widget.match.homeLineup == null || widget.match.awayLineup == null) {
      return const Center(
        child: Text('Lineup not available'),
      );
    }
    
    return LineupWidget(
      homeLineup: widget.match.homeLineup!,
      awayLineup: widget.match.awayLineup!,
      homeTeam: widget.match.homeTeam,
      awayTeam: widget.match.awayTeam,
    );
  }

  Widget _buildRatingsTab() {
    return const Center(
      child: Text('Player ratings coming soon'),
    );
  }

  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Match Summary',
            style: AppTextStyles.h3,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Quick Stats
          if (widget.match.statistics != null)
            _buildQuickStats(),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Key Events
          _buildKeyEvents(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Match Information',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildInfoRow('Date', widget.match.matchDate.toString().split(' ')[0]),
            _buildInfoRow('Time', '${widget.match.matchDate.hour}:${widget.match.matchDate.minute.toString().padLeft(2, '0')}'),
            _buildInfoRow('Status', widget.match.status),
            if (widget.match.venue != null)
              _buildInfoRow('Venue', widget.match.venue!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.secondaryText,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsTimeline() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Goals',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ...widget.match.goals.map((goal) => _buildGoalItem(goal)),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalItem(goal) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: AppColors.red,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.sports_soccer,
              size: 16,
              color: AppColors.white,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  goal.scorer.name,
                  style: AppTextStyles.bodyMedium,
                ),
                Text(
                  "${goal.minute}' - ${goal.team == 'home' ? widget.match.homeTeam.name : widget.match.awayTeam.name}",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVenueInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Venue',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              widget.match.venue!,
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    final stats = widget.match.statistics!;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Stats',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Shots', '${stats.homeTeam.shots} - ${stats.awayTeam.shots}'),
                _buildStatItem('On Target', '${stats.homeTeam.shotsOnTarget} - ${stats.awayTeam.shotsOnTarget}'),
                _buildStatItem('Possession', '${stats.homeTeam.possession}% - ${stats.awayTeam.possession}%'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.secondaryText,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildKeyEvents() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Key Events',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            if (widget.match.goals.isEmpty)
              Text(
                'No key events recorded',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.secondaryText,
                ),
              )
            else
              ...widget.match.goals.map((goal) => _buildGoalItem(goal)),
          ],
        ),
      ),
    );
  }
}
