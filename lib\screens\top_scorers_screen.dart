import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/player.dart';
import '../services/api_service.dart';
import '../widgets/league_selector.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';

class TopScorersScreen extends StatefulWidget {
  const TopScorersScreen({Key? key}) : super(key: key);

  @override
  State<TopScorersScreen> createState() => _TopScorersScreenState();
}

class _TopScorersScreenState extends State<TopScorersScreen> {
  String _selectedLeague = 'premier_league';
  List<TopScorer> _topScorers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTopScorers();
  }

  Future<void> _loadTopScorers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final scorers = await ApiService.getTopScorers(_selectedLeague);
      setState(() {
        _topScorers = scorers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading top scorers: $e')),
        );
      }
    }
  }

  void _onLeagueSelected(String league) {
    setState(() {
      _selectedLeague = league;
    });
    _loadTopScorers();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Top Scorers'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 0,
      ),
      body: Column(
        children: [
          // League Selector
          Container(
            color: AppColors.white,
            child: LeagueSelectorHorizontal(
              selectedLeague: _selectedLeague,
              onLeagueSelected: _onLeagueSelected,
              showAllOption: false,
            ),
          ),
          
          // Top Scorers List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _topScorers.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                        onRefresh: _loadTopScorers,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppConstants.paddingMedium),
                          itemCount: _topScorers.length,
                          itemBuilder: (context, index) {
                            final scorer = _topScorers[index];
                            return _buildScorerCard(scorer, index + 1);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildScorerCard(TopScorer scorer, int position) {
    final isTopScorer = position == 1;
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      elevation: isTopScorer ? 8 : 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          gradient: isTopScorer
              ? const LinearGradient(
                  colors: [AppColors.darkPurple, AppColors.darkNavy],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // Position Badge
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getPositionColor(position),
                  shape: BoxShape.circle,
                  boxShadow: isTopScorer
                      ? [
                          BoxShadow(
                            color: AppColors.darkPurple.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: Text(
                    position.toString(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              
              // Player Photo
              Container(
                width: AppConstants.playerImageMedium,
                height: AppConstants.playerImageMedium,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.lightGrey,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.grey.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: CachedNetworkImage(
                    imageUrl: scorer.player.photo ?? '',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppColors.lightGrey,
                      child: const Icon(
                        Icons.person,
                        color: AppColors.grey,
                        size: AppConstants.iconMedium,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppColors.lightGrey,
                      child: const Icon(
                        Icons.person,
                        color: AppColors.grey,
                        size: AppConstants.iconMedium,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              
              // Player Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      scorer.player.name,
                      style: AppTextStyles.playerName.copyWith(
                        color: isTopScorer ? AppColors.white : AppColors.primaryText,
                        fontWeight: isTopScorer ? FontWeight.bold : FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        // Team Logo
                        Container(
                          width: AppConstants.teamLogoSmall,
                          height: AppConstants.teamLogoSmall,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.white,
                          ),
                          child: ClipOval(
                            child: CachedNetworkImage(
                              imageUrl: scorer.team.logo,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppColors.lightGrey,
                                child: const Icon(
                                  Icons.sports_soccer,
                                  color: AppColors.grey,
                                  size: 12,
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppColors.lightGrey,
                                child: const Icon(
                                  Icons.sports_soccer,
                                  color: AppColors.grey,
                                  size: 12,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          scorer.team.shortName,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: isTopScorer
                                ? AppColors.white.withValues(alpha: 0.8)
                                : AppColors.secondaryText,
                          ),
                        ),
                      ],
                    ),
                    if (scorer.matches > 0) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${scorer.matches} matches • ${scorer.goalsPerMatch.toStringAsFixed(1)} goals/match',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: isTopScorer
                              ? AppColors.white.withValues(alpha: 0.7)
                              : AppColors.secondaryText,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Goals Count
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isTopScorer
                          ? AppColors.white.withValues(alpha: 0.2)
                          : AppColors.lightGrey,
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    child: Text(
                      scorer.goals.toString(),
                      style: AppTextStyles.h3.copyWith(
                        color: isTopScorer ? AppColors.white : AppColors.primaryText,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Goals',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isTopScorer
                          ? AppColors.white.withValues(alpha: 0.8)
                          : AppColors.secondaryText,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPositionColor(int position) {
    switch (position) {
      case 1:
        return AppColors.warning; // Gold
      case 2:
        return AppColors.grey; // Silver
      case 3:
        return const Color(0xFFCD7F32); // Bronze
      default:
        return AppColors.darkPurple;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.sports_soccer,
            size: 64,
            color: AppColors.grey,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No top scorers data available',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ElevatedButton(
            onPressed: _loadTopScorers,
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }
}
