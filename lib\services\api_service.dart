import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/match.dart';
import '../models/team.dart';
import '../models/player.dart';


class ApiService {
  static const String _baseUrl = 'https://api.football-data.org/v4';
  static const String _apiKey = 'YOUR_API_KEY_HERE'; // Replace with actual API key
  
  static final Map<String, String> _headers = {
    'X-Auth-Token': _apiKey,
    'Content-Type': 'application/json',
  };

  // Get matches for a specific league
  static Future<List<Match>> getMatches({
    String? leagueId,
    DateTime? dateFrom,
    DateTime? dateTo,
    String? status,
  }) async {
    try {
      String url = '$_baseUrl/matches';
      List<String> queryParams = [];
      
      if (leagueId != null) {
        queryParams.add('competitions=$leagueId');
      }
      if (dateFrom != null) {
        queryParams.add('dateFrom=${dateFrom.toIso8601String().split('T')[0]}');
      }
      if (dateTo != null) {
        queryParams.add('dateTo=${dateTo.toIso8601String().split('T')[0]}');
      }
      if (status != null) {
        queryParams.add('status=$status');
      }
      
      if (queryParams.isNotEmpty) {
        url += '?${queryParams.join('&')}';
      }

      final response = await http.get(Uri.parse(url), headers: _headers);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final matches = (data['matches'] as List)
            .map((matchJson) => Match.fromJson(matchJson))
            .toList();
        return matches;
      } else {
        throw Exception('Failed to load matches: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching matches: $e');
      return _getMockMatches(); // Return mock data for development
    }
  }

  // Get live matches
  static Future<List<Match>> getLiveMatches() async {
    return getMatches(status: 'LIVE');
  }

  // Get today's matches
  static Future<List<Match>> getTodayMatches() async {
    final today = DateTime.now();
    return getMatches(
      dateFrom: today,
      dateTo: today,
    );
  }

  // Get upcoming matches
  static Future<List<Match>> getUpcomingMatches() async {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final nextWeek = DateTime.now().add(const Duration(days: 7));
    return getMatches(
      dateFrom: tomorrow,
      dateTo: nextWeek,
      status: 'SCHEDULED',
    );
  }

  // Get match details
  static Future<Match?> getMatchDetails(int matchId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/matches/$matchId'),
        headers: _headers,
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Match.fromJson(data);
      } else {
        throw Exception('Failed to load match details: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching match details: $e');
      return null;
    }
  }

  // Get top scorers for a league
  static Future<List<TopScorer>> getTopScorers(String leagueId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/competitions/$leagueId/scorers'),
        headers: _headers,
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final scorers = (data['scorers'] as List)
            .map((scorerJson) => TopScorer.fromJson(scorerJson))
            .toList();
        return scorers;
      } else {
        throw Exception('Failed to load top scorers: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching top scorers: $e');
      return _getMockTopScorers(); // Return mock data for development
    }
  }

  // Get teams for a league
  static Future<List<Team>> getTeams(String leagueId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/competitions/$leagueId/teams'),
        headers: _headers,
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final teams = (data['teams'] as List)
            .map((teamJson) => Team.fromJson(teamJson))
            .toList();
        return teams;
      } else {
        throw Exception('Failed to load teams: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching teams: $e');
      return [];
    }
  }

  // Mock data for development
  static List<Match> _getMockMatches() {
    return [
      Match(
        id: 1,
        homeTeam: Team(
          id: 1,
          name: 'Manchester United',
          shortName: 'Man Utd',
          logo: 'assets/logos/man_utd.png',
        ),
        awayTeam: Team(
          id: 2,
          name: 'Nottingham Forest',
          shortName: 'Forest',
          logo: 'assets/logos/forest.png',
        ),
        matchDate: DateTime.now().add(const Duration(hours: 2)),
        status: 'LIVE',
        minute: 67,
        score: Score(homeScore: 3, awayScore: 2),
        league: 'Premier League',
        goals: [
          Goal(
            minute: 17,
            scorer: Player(id: 1, name: 'Eriksen'),
            team: 'home',
          ),
          Goal(
            minute: 52,
            scorer: Player(id: 2, name: 'Casemiro'),
            team: 'home',
          ),
          Goal(
            minute: 76,
            scorer: Player(id: 3, name: 'Fernandes'),
            team: 'home',
          ),
        ],
      ),
    ];
  }

  static List<TopScorer> _getMockTopScorers() {
    return [
      TopScorer(
        player: Player(id: 1, name: 'Harry Maguire'),
        team: Team(
          id: 1,
          name: 'Manchester United',
          shortName: 'Man Utd',
          logo: 'assets/logos/man_utd.png',
        ),
        goals: 18,
      ),
    ];
  }
}
