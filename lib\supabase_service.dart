import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/match.dart';
import '../models/team.dart';
import '../models/player.dart';
import '../constants/app_constants.dart';

class SupabaseService {
  static SupabaseClient get client => Supabase.instance.client;

  // Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );
  }

  // Save match data
  static Future<void> saveMatch(Match match) async {
    try {
      await client.from('matches').upsert(match.toJson());
    } catch (e) {
      debugPrint('Error saving match: $e');
      rethrow;
    }
  }

  // Get matches from database
  static Future<List<Match>> getMatches({
    String? league,
    DateTime? date,
    String? status,
  }) async {
    try {
      var query = client.from('matches').select();

      if (league != null) {
        query = query.eq('league', league);
      }
      if (date != null) {
        final dateStr = date.toIso8601String().split('T')[0];
        query = query.gte('match_date', '${dateStr}T00:00:00')
                    .lt('match_date', '${dateStr}T23:59:59');
      }
      if (status != null) {
        query = query.eq('status', status);
      }

      final response = await query.order('match_date');

      return (response as List)
          .map((json) => Match.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error fetching matches: $e');
      return [];
    }
  }

  // Save team data
  static Future<void> saveTeam(Team team) async {
    try {
      await client.from('teams').upsert(team.toJson());
    } catch (e) {
      debugPrint('Error saving team: $e');
      rethrow;
    }
  }

  // Get teams from database
  static Future<List<Team>> getTeams() async {
    try {
      final response = await client.from('teams').select();
      return (response as List)
          .map((json) => Team.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error fetching teams: $e');
      return [];
    }
  }

  // Save top scorers
  static Future<void> saveTopScorers(List<TopScorer> scorers) async {
    try {
      final data = scorers.map((scorer) => scorer.toJson()).toList();
      await client.from('top_scorers').upsert(data);
    } catch (e) {
      debugPrint('Error saving top scorers: $e');
      rethrow;
    }
  }

  // Get top scorers from database
  static Future<List<TopScorer>> getTopScorers({String? league}) async {
    try {
      var query = client.from('top_scorers').select();

      if (league != null) {
        query = query.eq('league', league);
      }

      final response = await query.order('goals', ascending: false);

      return (response as List)
          .map((json) => TopScorer.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error fetching top scorers: $e');
      return [];
    }
  }

  // Real-time subscriptions
  static Stream<List<Match>> subscribeToLiveMatches() {
    return client
        .from('matches')
        .stream(primaryKey: ['id'])
        .eq('status', 'LIVE')
        .map((data) => data.map((json) => Match.fromJson(json)).toList());
  }

  // Save user preferences
  static Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      await client.from('user_preferences').upsert(preferences);
    } catch (e) {
      debugPrint('Error saving user preferences: $e');
      rethrow;
    }
  }

  // Get user preferences
  static Future<Map<String, dynamic>?> getUserPreferences(String userId) async {
    try {
      final response = await client
          .from('user_preferences')
          .select()
          .eq('user_id', userId)
          .single();
      return response;
    } catch (e) {
      debugPrint('Error fetching user preferences: $e');
      return null;
    }
  }
}

