import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';

class LeagueSelector extends StatelessWidget {
  final String selectedLeague;
  final Function(String) onLeagueSelected;

  const LeagueSelector({
    Key? key,
    required this.selectedLeague,
    required this.onLeagueSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
        itemCount: AppConstants.leagues.length,
        itemBuilder: (context, index) {
          final leagueKey = AppConstants.leagues.keys.elementAt(index);
          final leagueName = AppConstants.leagues[leagueKey]!;
          final isSelected = selectedLeague == leagueKey;
          
          return Padding(
            padding: const EdgeInsets.only(right: AppConstants.paddingMedium),
            child: GestureDetector(
              onTap: () => onLeagueSelected(leagueKey),
              child: AnimatedContainer(
                duration: AppConstants.shortAnimation,
                width: 60,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.darkPurple : AppColors.white,
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                  border: Border.all(
                    color: isSelected ? AppColors.darkPurple : AppColors.mediumGrey,
                    width: 2,
                  ),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: AppColors.darkPurple.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ] : null,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // League Logo
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? AppColors.white : AppColors.lightGrey,
                      ),
                      child: ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: AppConstants.leagueLogos[leagueKey] ?? '',
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: AppColors.lightGrey,
                            child: Icon(
                              Icons.sports_soccer,
                              size: 20,
                              color: isSelected ? AppColors.darkPurple : AppColors.grey,
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: AppColors.lightGrey,
                            child: Icon(
                              Icons.sports_soccer,
                              size: 20,
                              color: isSelected ? AppColors.darkPurple : AppColors.grey,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    
                    // League Name
                    Text(
                      _getShortLeagueName(leagueName),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: isSelected ? AppColors.white : AppColors.primaryText,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getShortLeagueName(String fullName) {
    switch (fullName) {
      case 'Premier League':
        return 'EPL';
      case 'La Liga':
        return 'La Liga';
      case 'Bundesliga':
        return 'BL';
      case 'Serie A':
        return 'Serie A';
      case 'Ligue 1':
        return 'L1';
      case 'Champions League':
        return 'UCL';
      default:
        return fullName.length > 6 ? fullName.substring(0, 6) : fullName;
    }
  }
}

class LeagueSelectorHorizontal extends StatelessWidget {
  final String selectedLeague;
  final Function(String) onLeagueSelected;
  final bool showAllOption;

  const LeagueSelectorHorizontal({
    Key? key,
    required this.selectedLeague,
    required this.onLeagueSelected,
    this.showAllOption = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final leagues = Map<String, String>.from(AppConstants.leagues);
    if (showAllOption) {
      leagues['all'] = 'All Leagues';
    }

    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
        itemCount: leagues.length,
        itemBuilder: (context, index) {
          final leagueKey = leagues.keys.elementAt(index);
          final leagueName = leagues[leagueKey]!;
          final isSelected = selectedLeague == leagueKey;
          
          return Padding(
            padding: const EdgeInsets.only(right: AppConstants.paddingSmall),
            child: FilterChip(
              label: Text(
                leagueKey == 'all' ? 'All' : _getShortLeagueName(leagueName),
                style: AppTextStyles.bodySmall.copyWith(
                  color: isSelected ? AppColors.white : AppColors.primaryText,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  onLeagueSelected(leagueKey);
                }
              },
              backgroundColor: AppColors.lightGrey,
              selectedColor: AppColors.darkPurple,
              checkmarkColor: AppColors.white,
              side: BorderSide(
                color: isSelected ? AppColors.darkPurple : AppColors.mediumGrey,
              ),
            ),
          );
        },
      ),
    );
  }

  String _getShortLeagueName(String fullName) {
    switch (fullName) {
      case 'Premier League':
        return 'Premier League';
      case 'La Liga':
        return 'La Liga';
      case 'Bundesliga':
        return 'Bundesliga';
      case 'Serie A':
        return 'Serie A';
      case 'Ligue 1':
        return 'Ligue 1';
      case 'Champions League':
        return 'Champions League';
      default:
        return fullName;
    }
  }
}
