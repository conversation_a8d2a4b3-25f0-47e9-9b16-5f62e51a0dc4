import 'package:flutter/material.dart';
import '../models/statistics.dart';
import '../models/team.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';

class LineupWidget extends StatelessWidget {
  final List<LineupPlayer> homeLineup;
  final List<LineupPlayer> awayLineup;
  final Team homeTeam;
  final Team awayTeam;

  const LineupWidget({
    Key? key,
    required this.homeLineup,
    required this.awayLineup,
    required this.homeTeam,
    required this.awayTeam,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          // Formation Display
          _buildFormationDisplay(),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Player Lists
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Home Team
              Expanded(
                child: _buildTeamLineup(
                  homeTeam.name,
                  homeLineup,
                  AppColors.liveGreen,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              
              // Away Team
              Expanded(
                child: _buildTeamLineup(
                  awayTeam.name,
                  awayLineup,
                  AppColors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormationDisplay() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: AppColors.liveGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppColors.white, width: 2),
      ),
      child: Stack(
        children: [
          // Field markings
          _buildFieldMarkings(),
          
          // Home team players (left side)
          ..._buildFormationPlayers(homeLineup, true),
          
          // Away team players (right side)
          ..._buildFormationPlayers(awayLineup, false),
        ],
      ),
    );
  }

  Widget _buildFieldMarkings() {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: FieldPainter(),
      ),
    );
  }

  List<Widget> _buildFormationPlayers(List<LineupPlayer> lineup, bool isHome) {
    final starters = lineup.where((p) => !p.isSubstitute).toList();
    final positions = _getPlayerPositions(starters, isHome);
    
    return positions.map((position) {
      final player = position['player'] as LineupPlayer;
      final x = position['x'] as double;
      final y = position['y'] as double;
      
      return Positioned(
        left: x,
        top: y,
        child: _buildPlayerDot(player, isHome),
      );
    }).toList();
  }

  Widget _buildPlayerDot(LineupPlayer player, bool isHome) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: isHome ? AppColors.liveGreen : AppColors.red,
        shape: BoxShape.circle,
        border: Border.all(color: AppColors.white, width: 2),
      ),
      child: Center(
        child: Text(
          player.shirtNumber.toString(),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getPlayerPositions(List<LineupPlayer> players, bool isHome) {
    // Simplified formation positioning
    final positions = <Map<String, dynamic>>[];
    
    // Basic 4-4-2 formation positions
    final formationPositions = [
      // Goalkeeper
      {'x': isHome ? 20.0 : 240.0, 'y': 130.0},
      // Defenders
      {'x': isHome ? 60.0 : 200.0, 'y': 80.0},
      {'x': isHome ? 60.0 : 200.0, 'y': 110.0},
      {'x': isHome ? 60.0 : 200.0, 'y': 150.0},
      {'x': isHome ? 60.0 : 200.0, 'y': 180.0},
      // Midfielders
      {'x': isHome ? 120.0 : 140.0, 'y': 70.0},
      {'x': isHome ? 120.0 : 140.0, 'y': 100.0},
      {'x': isHome ? 120.0 : 140.0, 'y': 160.0},
      {'x': isHome ? 120.0 : 140.0, 'y': 190.0},
      // Forwards
      {'x': isHome ? 180.0 : 80.0, 'y': 100.0},
      {'x': isHome ? 180.0 : 80.0, 'y': 160.0},
    ];
    
    for (int i = 0; i < players.length && i < formationPositions.length; i++) {
      positions.add({
        'player': players[i],
        'x': formationPositions[i]['x'],
        'y': formationPositions[i]['y'],
      });
    }
    
    return positions;
  }

  Widget _buildTeamLineup(String teamName, List<LineupPlayer> lineup, Color teamColor) {
    final starters = lineup.where((p) => !p.isSubstitute).toList();
    final substitutes = lineup.where((p) => p.isSubstitute).toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Team Header
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: teamColor,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Text(
                teamName,
                style: AppTextStyles.h4.copyWith(
                  color: AppColors.white,
                ),
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Starting XI
            Text(
              'Starting XI',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            ...starters.map((player) => _buildPlayerItem(player, teamColor)),
            
            if (substitutes.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Text(
                'Substitutes',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              ...substitutes.map((player) => _buildPlayerItem(player, teamColor)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerItem(LineupPlayer player, Color teamColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // Jersey Number
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: teamColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                player.shirtNumber.toString(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.paddingSmall),
          
          // Player Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  player.name,
                  style: AppTextStyles.bodyMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  player.position,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class FieldPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.white
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Center line
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      paint,
    );

    // Center circle
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      40,
      paint,
    );

    // Goal areas
    final goalWidth = size.height * 0.3;
    const goalHeight = 30.0;
    
    // Left goal area
    canvas.drawRect(
      Rect.fromLTWH(
        0,
        (size.height - goalWidth) / 2,
        goalHeight,
        goalWidth,
      ),
      paint,
    );
    
    // Right goal area
    canvas.drawRect(
      Rect.fromLTWH(
        size.width - goalHeight,
        (size.height - goalWidth) / 2,
        goalHeight,
        goalWidth,
      ),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
