import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/match.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';

class MatchCard extends StatelessWidget {
  final Match match;
  final VoidCallback? onTap;
  final bool isLive;

  const MatchCard({
    Key? key,
    required this.match,
    this.onTap,
    this.isLive = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            gradient: isLive ? AppColors.liveGradient : null,
          ),
          child: Column(
            children: [
              // League and Time Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    match.league ?? 'Unknown League',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isLive ? AppColors.white : AppColors.secondaryText,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isLive ? AppColors.red : AppColors.lightGrey,
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    child: Text(
                      match.displayTime,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: isLive ? AppColors.white : AppColors.primaryText,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Teams and Score Row
              Row(
                children: [
                  // Home Team
                  Expanded(
                    child: _buildTeamSection(
                      team: match.homeTeam,
                      isHome: true,
                      isLive: isLive,
                    ),
                  ),
                  
                  // Score Section
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        if (match.score != null)
                          Text(
                            match.score!.displayScore,
                            style: AppTextStyles.scoreText.copyWith(
                              color: isLive ? AppColors.white : AppColors.primaryText,
                            ),
                          )
                        else
                          Text(
                            'vs',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: isLive ? AppColors.white : AppColors.secondaryText,
                            ),
                          ),
                        if (match.isLive && match.minute != null)
                          Text(
                            "${match.minute}'",
                            style: AppTextStyles.minuteText.copyWith(
                              color: AppColors.white,
                            ),
                          ),
                      ],
                    ),
                  ),
                  
                  // Away Team
                  Expanded(
                    child: _buildTeamSection(
                      team: match.awayTeam,
                      isHome: false,
                      isLive: isLive,
                    ),
                  ),
                ],
              ),
              
              // Goals Section (if any)
              if (match.goals.isNotEmpty) ...[
                const SizedBox(height: AppConstants.paddingSmall),
                _buildGoalsSection(isLive),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTeamSection({
    required team,
    required bool isHome,
    required bool isLive,
  }) {
    return Column(
      children: [
        // Team Logo
        Container(
          width: AppConstants.teamLogoMedium,
          height: AppConstants.teamLogoMedium,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.white,
            boxShadow: [
              BoxShadow(
                color: AppColors.grey.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipOval(
            child: CachedNetworkImage(
              imageUrl: team.logo,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppColors.lightGrey,
                child: const Icon(
                  Icons.sports_soccer,
                  color: AppColors.grey,
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: AppColors.lightGrey,
                child: const Icon(
                  Icons.sports_soccer,
                  color: AppColors.grey,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        
        // Team Name
        Text(
          team.shortName,
          style: AppTextStyles.teamName.copyWith(
            color: isLive ? AppColors.white : AppColors.primaryText,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildGoalsSection(bool isLive) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingSmall),
      decoration: BoxDecoration(
        color: isLive
            ? AppColors.white.withValues(alpha: 0.1)
            : AppColors.lightGrey,
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
      ),
      child: Column(
        children: match.goals.map((goal) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: AppColors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.sports_soccer,
                    size: 12,
                    color: AppColors.white,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  "${goal.minute}' ${goal.scorer.name}",
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isLive ? AppColors.white : AppColors.primaryText,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
