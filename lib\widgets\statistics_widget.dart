import 'package:flutter/material.dart';
import '../models/statistics.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_constants.dart';

class StatisticsWidget extends StatelessWidget {
  final MatchStatistics statistics;

  const StatisticsWidget({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.statisticsBackground,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            _buildStatisticRow(
              'Shots',
              statistics.homeTeam.shots,
              statistics.awayTeam.shots,
            ),
            _buildStatisticRow(
              'Shots on Target',
              statistics.homeTeam.shotsOnTarget,
              statistics.awayTeam.shotsOnTarget,
            ),
            _buildPossessionBar(),
            _buildStatisticRow(
              'Corners',
              statistics.homeTeam.corners,
              statistics.awayTeam.corners,
            ),
            _buildStatisticRow(
              'Fouls',
              statistics.homeTeam.fouls,
              statistics.awayTeam.fouls,
            ),
            _buildStatisticRow(
              'Yellow Cards',
              statistics.homeTeam.yellowCards,
              statistics.awayTeam.yellowCards,
            ),
            _buildStatisticRow(
              'Red Cards',
              statistics.homeTeam.redCards,
              statistics.awayTeam.redCards,
            ),
            _buildStatisticRow(
              'Offsides',
              statistics.homeTeam.offsides,
              statistics.awayTeam.offsides,
            ),
            _buildStatisticRow(
              'Passes',
              statistics.homeTeam.passes,
              statistics.awayTeam.passes,
            ),
            _buildStatisticRow(
              'Pass Accuracy',
              statistics.homeTeam.passAccuracy,
              statistics.awayTeam.passAccuracy,
              isPercentage: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    String label,
    int homeValue,
    int awayValue, {
    bool isPercentage = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          // Label
          Text(
            label,
            style: AppTextStyles.statisticLabel.copyWith(
              color: AppColors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          // Values
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isPercentage ? '$homeValue%' : homeValue.toString(),
                style: AppTextStyles.statisticValue,
              ),
              Text(
                isPercentage ? '$awayValue%' : awayValue.toString(),
                style: AppTextStyles.statisticValue,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          // Progress Bar
          _buildProgressBar(homeValue, awayValue),
        ],
      ),
    );
  }

  Widget _buildPossessionBar() {
    final homePos = statistics.homeTeam.possession;
    final awayPos = statistics.awayTeam.possession;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        children: [
          // Label
          Text(
            'Possession',
            style: AppTextStyles.statisticLabel.copyWith(
              color: AppColors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          // Percentage Values
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$homePos%',
                style: AppTextStyles.statisticValue,
              ),
              Text(
                '$awayPos%',
                style: AppTextStyles.statisticValue,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          // Possession Bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppColors.white.withValues(alpha: 0.2),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: homePos,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: AppColors.liveGreen,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        bottomLeft: Radius.circular(4),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: awayPos,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: AppColors.red,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(4),
                        bottomRight: Radius.circular(4),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(int homeValue, int awayValue) {
    final total = homeValue + awayValue;
    if (total == 0) {
      return Container(
        height: 4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2),
          color: AppColors.white.withValues(alpha: 0.2),
        ),
      );
    }
    
    final homePercentage = homeValue / total;
    final awayPercentage = awayValue / total;
    
    return Container(
      height: 4,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        color: AppColors.white.withValues(alpha: 0.2),
      ),
      child: Row(
        children: [
          Expanded(
            flex: (homePercentage * 100).round(),
            child: Container(
              decoration: const BoxDecoration(
                color: AppColors.liveGreen,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(2),
                  bottomLeft: Radius.circular(2),
                ),
              ),
            ),
          ),
          Expanded(
            flex: (awayPercentage * 100).round(),
            child: Container(
              decoration: const BoxDecoration(
                color: AppColors.red,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(2),
                  bottomRight: Radius.circular(2),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class StatisticCard extends StatelessWidget {
  final String title;
  final String homeValue;
  final String awayValue;
  final double? homePercentage;
  final double? awayPercentage;

  const StatisticCard({
    Key? key,
    required this.title,
    required this.homeValue,
    required this.awayValue,
    this.homePercentage,
    this.awayPercentage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.darkNavy,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  homeValue,
                  style: AppTextStyles.h3.copyWith(
                    color: AppColors.white,
                  ),
                ),
                Text(
                  awayValue,
                  style: AppTextStyles.h3.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ],
            ),
            if (homePercentage != null && awayPercentage != null) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              LinearProgressIndicator(
                value: homePercentage! / (homePercentage! + awayPercentage!),
                backgroundColor: AppColors.red,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.liveGreen),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
