name: balbalan
description: "تطبيق Balbalan الاحترافي لعشاق كرة القدم"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2

  # State Management
  provider: ^6.1.1

  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2

  # Database
  supabase_flutter: ^2.0.0

  # Firebase
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  firebase_analytics: ^10.7.4

  # UI & Animations
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # Navigation
  go_router: ^12.1.3

  # Utilities
  shared_preferences: ^2.2.2
  connectivity_plus: ^5.0.2
  package_info_plus: ^4.2.0

  # WebSocket for real-time updates
  web_socket_channel: ^2.4.0

  # Charts for statistics
  fl_chart: ^0.65.0

  # Date formatting
  timeago: ^3.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/

  # Fonts
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700

flutter_intl:
  enabled: true
